import pyautogui as p
import time
import win32api
import sys

print("Click to get the data:")
LastPosition = p.position(1, 1)

while True:  # Start loop
    a = win32api.GetKeyState(0x01)

    if a < 0 and LastPosition != p.position():
        x, y = p.position()
        rgb_1 = p.pixel(x, y)
        print(f"first=({x}, {y}) ; rgb colors={rgb_1}")
        print("...")
        time.sleep(1.5)
        while True:  # Start loop
            a = win32api.GetKeyState(0x01)
            if a < 0 and LastPosition != p.position():
                if x:
                    x_2, y_2 = p.position()
                    rgb_2 = p.pixel(x_2, y_2)

                    x_range = x_2 - x
                    y_range = y_2 - y
                    print(f"second=({x_2}, {y_2}) ; rgb colors={rgb_2}")
                    print(f"region=({x}, {y}, {x_range}, {y_range})")
                LastPosition = p.position(x, y)
                time.sleep(1.5)
                sys.exit()
