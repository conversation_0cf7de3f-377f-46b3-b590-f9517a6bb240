import pandas as pd
import pyautogui as p
import time
import sys
import win32gui as wg

"""
Instructions:

"""

INPUT_FILE = r"C:\zf_projects\SAP_add_route_LIM\routes.csv"


def get_list_of_parts():
    df = pd.read_csv(INPUT_FILE, dtype=str, sep=";")
    input_data = df.values.tolist()
    return input_data


def check_mouse_state():
    time.sleep(1)
    while True:
        state = wg.GetCursorInfo()[1]
        if state == 65561:
            time.sleep(1)
        else:
            time.sleep(1)
            return


def check_main_screen():
    image = r"C:\zf_projects\SAP_add_route_LIM\img\routes_main.png"
    region = (5, 75, 215, 30)
    while True:
        confirm_on_screen = False
        try:
            confirm_on_screen = p.locateOnScreen(image, confidence=0.80, region=region, grayscale=True)
        except Exception as e:
            print(e)
        if confirm_on_screen:
            return True
        else:
            time.sleep(1)


def check_overview_screen():
    image = r"C:\zf_projects\SAP_add_route_LIM\img\routes_overview.png"
    region = (5, 70, 245, 30)
    while True:
        confirm_on_screen = False
        try:
            confirm_on_screen = p.locateOnScreen(image, confidence=0.80, region=region, grayscale=True)
        except Exception as e:
            print(e)
        if confirm_on_screen:
            return True
        else:
            time.sleep(1)


def check_create_screen():
    image = r"C:\zf_projects\SAP_add_route_LIM\img\routes_create.png"
    region = (5, 70, 320, 30)
    while True:
        confirm_on_screen = False
        try:
            confirm_on_screen = p.locateOnScreen(image, confidence=0.80, region=region, grayscale=True)
        except Exception as e:
            print(e)
        if confirm_on_screen:
            return True
        else:
            time.sleep(1)


# def check_cs15_screen_status():
#     list_image = r"C:\zf_projects\SAP_serial_number_cs15\img\cs15_material_list_screen.png"
#     error_image = r"C:\zf_projects\SAP_serial_number_cs15\img\error_icon.png"
#     list_region = (1, 71, 94, 198)
#     error_region = (3, 1112, 81, 34)

#     while True:
#         try:
#             # Check for material list screen
#             if p.locateOnScreen(list_image, confidence=0.60, region=list_region, grayscale=True):
#                 return "cs15_found"
#         except Exception as e:
#             print(e)

#         try:
#             # Check for error icon
#             if p.locateOnScreen(error_image, confidence=0.60, region=error_region, grayscale=True):
#                 return "cs15_not_found"
#         except Exception as e:
#             print(e)

#         time.sleep(1)


def task(item):
    if check_main_screen():
        p.click(850, 90)
        time.sleep(0.3)
        p.hotkey("ctrl", "a")
        time.sleep(0.3)
        p.press("del")
        p.typewrite(item[1], 0.03)
        p.press("enter")
        time.sleep(0.3)
        p.press("down")
        p.typewrite(item[0], 0.03)
        p.press("enter")
        time.sleep(0.5)
        p.press("f8")

        check_overview_screen()
        p.click(420, 160)

        check_create_screen()
        p.typewrite(item[1], 0.03)
        p.press("enter")
        check_mouse_state()
        p.press("down")
        p.typewrite(item[0], 0.03)
        p.press("enter")
        check_mouse_state()
        p.click(55, 285)
        check_mouse_state()
        p.press("tab")
        p.typewrite(item[2], 0.03)
        p.press("enter")
        check_mouse_state()
        p.click(55, 285)
        check_mouse_state()
        p.press("tab")
        p.press("down")
        p.typewrite(item[3], 0.03)
        p.press("enter")
        check_mouse_state()
        p.click(725, 195)
        check_mouse_state()
        p.hotkey("ctrl", "s")
        check_mouse_state()
        p.press("enter")
        check_mouse_state()
        p.press("f3")
        check_mouse_state()
        p.press("f3")


def main():
    list_of_parts = get_list_of_parts()
    for item in list_of_parts:
        print("Working on item: ", item)
        task(item)


if __name__ == "__main__":
    main()
    print("Done.")
    sys.exit()
